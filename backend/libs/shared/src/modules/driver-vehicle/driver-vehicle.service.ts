import { Injectable, NotFoundException } from '@nestjs/common';
import { DriverVehicleRepository } from '../../repositories/driver-vehicle.repository';
import { DriverVehicle } from '../../repositories/models/driverVehicle.model';
import { UserProfileService } from '../user-profile/user-profile.service';

@Injectable()
export class DriverVehicleService {
  constructor(
    private readonly driverVehicleRepository: DriverVehicleRepository,
    private readonly userProfileService: UserProfileService,
  ) {}

  async createDriverVehicle(userId: string, dto: any): Promise<DriverVehicle> {
    // Map DTO to repository input, ensuring userId is set
    const userProfile =
      await this.userProfileService.findDriverProfileByUserId(userId);
    if (!userProfile) {
      throw new NotFoundException(
        `User profile for user ID ${userId} not found`,
      );
    }
    // need to update if profileid and vehicle type id record exist
    const existingVehicle = await this.driverVehicleRepository.findOne({
      where: {
        userProfileId: userProfile.id,
        vehicleTypeId: dto.vehicleTypeId,
      },
    });
    if (existingVehicle) {
      // Update the existing vehicle record instead of creating a new one
      return this.driverVehicleRepository.updateDriverVehicle(
        existingVehicle.id,
        {
          ...dto,
          userProfileId: userProfile.id,
        },
      );
    }
    // Create a new driver vehicle record
    return this.driverVehicleRepository.createDriverVehicle({
      ...dto,
      userProfileId: userProfile.id,
    });
  }

  async deleteDriverVehicle(id: string): Promise<DriverVehicle> {
    const driverVehicle =
      await this.driverVehicleRepository.findDriverVehicleById(id);
    if (!driverVehicle) {
      throw new NotFoundException(`DriverVehicle with ID ${id} not found`);
    }
    return this.driverVehicleRepository.deleteDriverVehicle(id);
  }

  /**
   * Find driver vehicles by user profile ID with vehicle type relation
   */
  async findDriverVehiclesByUserProfileId(
    userProfileId: string,
  ): Promise<DriverVehicle[]> {
    // Validate that the user profile exists
    await this.userProfileService.findUserProfileById(userProfileId);

    return this.driverVehicleRepository.findMany({
      where: {
        userProfileId,
      },
      include: {
        vehicleType: true,
        cityProduct: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Find driver vehicle by ID with relations
   */
  async findDriverVehicleByIdWithRelations(id: string): Promise<DriverVehicle> {
    const driverVehicle = await this.driverVehicleRepository.findById(id, {
      include: {
        vehicleType: true,
        cityProduct: true,
        userProfile: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!driverVehicle) {
      throw new NotFoundException(`Driver vehicle with ID ${id} not found`);
    }

    return driverVehicle;
  }
}
