import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';
import { CreateDriverVehicleDto } from './dto/create-driver-vehicle.dto';
import { UploadNocDto } from './dto/upload-noc.dto';
import { DriverVehicleService } from '@shared/shared/modules/driver-vehicle/driver-vehicle.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { DriverVehicleDocumentService } from '@shared/shared/modules/driver-vehicle-document/driver-vehicle-document.service';

@ApiTags('Driver Vehicles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('driver-vehicles')
export class DriverVehicleController {
  constructor(
    private readonly driverVehicleService: DriverVehicleService,
    private readonly driverVehicleDocumentService: DriverVehicleDocumentService,
  ) {}

  @Patch('vehicle')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a driver vehicle for the onboarded user' })
  @ApiResponse({
    status: 201,
    description: 'Driver vehicle created successfully',
    schema: {
      example: {
        success: true,
        message: 'Driver vehicle created',
        data: {
          id: 'driver-vehicle-id',
          userId: 'user-id',
          cityId: 'city-id',
          vehicleId: 'vehicle-id',
          vehicleNumber: 'MH12AB1234',
          isNocRequired: false,
          nocFileUrl: null,
          isPrimary: false,
          type: 'normal',
          status: 'pending',
          createdAt: '2023-12-01T10:00:00Z',
          updatedAt: '2023-12-01T10:00:00Z',
          deletedAt: null,
        },
        timestamp: 1690000000000,
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createDriverVehicle(
    @Body() dto: CreateDriverVehicleDto,
    @Req() req: Request,
  ) {
    const userId = (req.user as any)?.sub || (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }
    const driverVehicle = await this.driverVehicleService.createDriverVehicle(
      userId,
      dto,
    );
    return {
      success: true,
      message: 'Driver vehicle created',
      data: driverVehicle,
      timestamp: Date.now(),
    };
  }

  @Post(':driverVehicleId/verify-rc')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify and save driver vehicle document by driver vehicle ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicle document verified and saved successfully',
  })
  async verifyAndSave(@Param('driverVehicleId') driverVehicleId: string) {
    const doc =
      await this.driverVehicleDocumentService.verifyAndSaveDocument(
        driverVehicleId,
      );
    return {
      success: true,
      message: 'Driver vehicle document verified and saved successfully',
      data: doc,
      timestamp: Date.now(),
    };
  }

  @Post(':driverVehicleId/upload-noc')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Upload NOC document for driver vehicle',
    description:
      'Upload or update NOC (No Objection Certificate) document for a driver vehicle. If an NOC document already exists, it will be updated with the new document URL.',
  })
  @ApiResponse({
    status: 200,
    description: 'NOC document uploaded successfully',
    schema: {
      example: {
        success: true,
        message: 'NOC document uploaded successfully',
        data: {
          id: 'driver-vehicle-document-id',
          driverVehicleId: 'driver-vehicle-id',
          vehicleDocumentId: 'noc-document-type-id',
          documentUrl:
            'https://s3.amazonaws.com/bucket/uploads/noc-document.pdf',
          documentFields: null,
          details: {
            uploadedAt: '2023-12-01T10:00:00Z',
            documentType: 'noc',
          },
          createdAt: '2023-12-01T10:00:00Z',
          updatedAt: '2023-12-01T10:00:00Z',
          deletedAt: null,
        },
        timestamp: 1690000000000,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found or NOC document type not found',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async uploadNoc(
    @Param('driverVehicleId') driverVehicleId: string,
    @Body() uploadNocDto: UploadNocDto,
  ) {
    const doc = await this.driverVehicleDocumentService.uploadNocDocument(
      driverVehicleId,
      uploadNocDto.documentUrl,
    );
    return {
      success: true,
      message: 'NOC document uploaded successfully',
      data: doc,
      timestamp: Date.now(),
    };
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List driver vehicles for authenticated user',
    description:
      'Get all driver vehicles for the authenticated user with vehicle type information. The user profile ID is extracted from the JWT token.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicles retrieved successfully',
  })
  @ApiQuery({
    name: 'profileId',
    required: true,
    type: String,
    description: 'User profile ID to filter driver KYC documents',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getDriverVehicles(@Query('profileId') profileId: string) {
    if (!profileId) {
      throw new BadRequestException('profileId query parameter is required');
    }

    const driverVehicles =
      await this.driverVehicleService.findDriverVehiclesByUserProfileId(
        profileId,
      );

    return {
      success: true,
      message: 'Driver vehicles retrieved successfully',
      data: driverVehicles,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver vehicle by ID',
    description:
      'Get a specific driver vehicle by its ID with all related information.',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicle retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Driver vehicle not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getDriverVehicleById(@Param('id') id: string) {
    const driverVehicle =
      await this.driverVehicleService.findDriverVehicleByIdWithRelations(id);

    return {
      success: true,
      message: 'Driver vehicle retrieved successfully',
      data: driverVehicle,
      timestamp: Date.now(),
    };
  }

  @Get(':id/documents')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List vehicle documents for a driver vehicle',
    description:
      'Get all vehicle documents with their associated driver vehicle documents for a specific driver vehicle ID. Document URLs are returned as signed S3 URLs for secure access.',
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle documents retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Driver vehicle not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getVehicleDocuments(@Param('id') driverVehicleId: string) {
    const documents =
      await this.driverVehicleDocumentService.getVehicleDocumentsWithDriverDocs(
        driverVehicleId,
      );

    return {
      success: true,
      message: 'Vehicle documents retrieved successfully',
      data: documents,
      timestamp: Date.now(),
    };
  }
}
