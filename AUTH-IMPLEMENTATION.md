// forgot password.

'use client';

import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from '@/lib/toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { ArrowLeft } from 'lucide-react';
import { Spinner } from '@/components/ui/spinner';
import { useForgotPassword } from '../api/mutations';
import { ErrorMessage } from '@/components/error-message';
import React from 'react';
import { AuthHeader } from '../components/header';
import { Input } from '@/components/ui/input';

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export function ForgotPasswordPage() {
  const {
    handleSubmit,
    register,
    formState: { errors },
    reset,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const forgotPasswordMutation = useForgotPassword();

  const onSubmit = (data: ForgotPasswordFormData) => {
    const payload = {
      ...data,
    };

    forgotPasswordMutation.mutate(payload, {
      onSuccess: () => {
        reset({});
        toast.success('Password reset link send successfully');
      },
      onError: (error) => {
        toast.error(error);
      },
    });
  };

  return (
    <div className="grid min-h-screen w-full">
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full max-w-md space-y-6 p-2 sm:p-6">
          <AuthHeader />

          <Card className="w-full">
            <CardHeader className="space-y-1 text-center">
              <CardTitle className="text-2xl font-semibold mb-2">
                Forgot password?
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Enter email address to receive a password reset link
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="space-y-1.5">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="Enter your email"
                  />
                  <ErrorMessage error={errors.email} />
                </div>

                <Button
                  className="w-full text-base"
                  size="lg"
                  type="submit"
                  disabled={forgotPasswordMutation.isPending}
                >
                  {forgotPasswordMutation.isPending ? (
                    <>
                      Sending...
                      <Spinner className="ml-2" />
                    </>
                  ) : (
                    'Send reset link'
                  )}
                </Button>
              </form>

              <Link
                href="/"
                className="flex items-center justify-center gap-2 text-sm text-primary hover:underline"
              >
                <ArrowLeft className="h-4 w-4" />
                Go back to Login
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}


// verify otp page

'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { Spinner } from '@/components/ui/spinner';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from '@/lib/toast';
import * as z from 'zod';
import { useResendOtp, useVerifyOtp } from '../api/mutations';
import { errorParser } from '@/lib/error-parser';
import { useRouter } from 'next/navigation';
import { ErrorMessage } from '@/components/error-message';
import { useAuthStore } from '@/store/auth-store';
import { AuthHeader } from '../components/header';

const verifyOtpSchema = z.object({
  otp: z.string().length(6, 'Please enter a valid OTP'),
});

type VerifyOtpFormData = z.infer<typeof verifyOtpSchema>;

const formatPhoneNumber = (phoneNumber: string) => {
  const countryCode = phoneNumber.startsWith('+')
    ? phoneNumber.slice(0, 3)
    : phoneNumber.slice(0, 2);
  const number = phoneNumber.slice(countryCode.length);
  const lastThreeDigits = number.slice(-3);
  const maskedPart = '*'.repeat(Math.max(0, number.length - 3));
  return `${countryCode}${maskedPart}${lastThreeDigits}`;
};

export function VerifyOtpPage() {
  const [timeLeft, setTimeLeft] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const authStore = useAuthStore((state) => state);

  const router = useRouter();
  const form = useForm<VerifyOtpFormData>({
    resolver: zodResolver(verifyOtpSchema),
    defaultValues: {
      otp: '',
    },
  });
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    setFocus,
  } = form;

  const verifyOtpMutation = useVerifyOtp();
  const resendOtpMutation = useResendOtp();

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  const onSubmit = (data: VerifyOtpFormData) => {
    if (!authStore.userId) {
      toast.error('User not found');
      return;
    }

    const userId = String(authStore.userId);

    const payload = {
      otp: data.otp,
      userId,
    };

    verifyOtpMutation.mutate(payload, {
      onSuccess: (response) => {
        const { accessToken, refreshToken } = response;

        toast.success('OTP verified successfully');
        useAuthStore.getState().setToken(accessToken, refreshToken);
        router.push('/dashboard/home');
      },
      onError: (error) => {
        toast.error(error);
      },
    });
  };

  const handleResendOtp = () => {
    if (!canResend) return;
    if (!authStore.userId) {
      toast.error('User not found');
      return;
    }

    const userId = authStore.userId;

    resendOtpMutation.mutate(
      { userId },
      {
        onSuccess: () => {
          setValue('otp', '');
          setFocus('otp');
          toast.success('OTP resend successfully');
          setTimeLeft(30);
          setCanResend(false);
        },
        onError: (error) => {
          toast.error(errorParser(error.message));
        },
      },
    );
  };

  return (
    <div className="grid min-h-screen w-full">
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full max-w-md space-y-8 p-6">
          <AuthHeader />

          <Card className="w-full">
            <CardHeader className="space-y-3">
              <CardTitle className="text-2xl font-semibold text-center">
                Verify OTP
              </CardTitle>
              <p className="text-center text-muted-foreground">
                Enter the OTP sent to{' '}
                <span className="font-medium text-muted-foreground">
                  {formatPhoneNumber(authStore.phoneNumber ?? '')}
                </span>
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <Form {...form}>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <FormField
                      control={control}
                      name="otp"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <InputOTP maxLength={6} {...field}>
                              <InputOTPGroup className="flex justify-center gap-4 w-full">
                                {[1, 2, 3, 4, 5, 6].map((item, index) => (
                                  <InputOTPSlot
                                    key={index}
                                    index={index}
                                    className="rounded-md border border-input"
                                  />
                                ))}
                              </InputOTPGroup>
                            </InputOTP>
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <ErrorMessage error={errors.otp} />
                  </div>

                  <div className="space-y-4">
                    <Button
                      className="w-full text-base"
                      size="lg"
                      type="submit"
                      disabled={verifyOtpMutation.isPending}
                    >
                      {verifyOtpMutation.isPending ? (
                        <>
                          Verifying...
                          <Spinner className="ml-2" />
                        </>
                      ) : (
                        'Verify OTP'
                      )}
                    </Button>

                    <div className="text-center space-y-3">
                      <p className="text-sm text-muted-foreground">
                        Didn&apos;t receive the OTP?{' '}
                        <Button
                          type="button"
                          variant="link"
                          className="p-0 h-auto font-semibold"
                          disabled={!canResend || resendOtpMutation.isPending}
                          onClick={handleResendOtp}
                        >
                          {resendOtpMutation.isPending ? (
                            <>
                              Resending...
                              <Spinner className="ml-2 h-3 w-3" />
                            </>
                          ) : canResend ? (
                            'Resend OTP'
                          ) : (
                            `Resend in ${timeLeft}s`
                          )}
                        </Button>
                      </p>

                      <Link
                        href="/"
                        className="flex items-center justify-center gap-2 text-sm text-primary hover:underline"
                      >
                        <ArrowLeft className="h-4 w-4" />
                        Back to Login
                      </Link>
                    </div>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}


// reset password page
'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, CheckCircle, EyeIcon, EyeOffIcon } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from '@/lib/toast';
import * as z from 'zod';
import { useResetPassword } from '../api/mutations';
import { ErrorMessage } from '@/components/error-message';
import { AuthHeader } from '../components/header';
import { passwordSchema } from '@/schema/password-schema';

const resetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

export function ResetPasswordPage() {
  const [isSuccess, setIsSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const resetPasswordMutation = useResetPassword();

  const onSubmit = (data: ResetPasswordFormData) => {
    const resetPasswordData = {
      password: data.password,
      token: token,
    };

    resetPasswordMutation.mutate(resetPasswordData, {
      onSuccess: () => {
        setIsSuccess(true);
        toast.success('Password reset successfully');
      },
      onError: (error) => {
        toast.error(error);
      },
    });
  };

  const handleBackToLogin = () => {
    router.push('/');
  };

  return (
    <div className="grid min-h-screen w-full">
      <div className="flex items-center justify-center">
        <div className="mx-auto w-full max-w-md space-y-6 p-2 sm:p-6">
          <AuthHeader />

          <Card className="w-full">
            {isSuccess ? (
              <CardContent className="flex flex-col items-center justify-center space-y-4 pt-6">
                <CheckCircle className="h-16 w-16 text-green-500" />
                <CardTitle className="text-2xl font-semibold text-center">
                  Password Reset Successful
                </CardTitle>
                <p className="text-center text-muted-foreground">
                  Your password has been successfully reset. You can now log in
                  with your new password.
                </p>
                <Button
                  className="w-full text-base"
                  size="lg"
                  variant={'link'}
                  onClick={handleBackToLogin}
                >
                  <ArrowLeft />
                  Back to Login
                </Button>
              </CardContent>
            ) : (
              <>
                <CardHeader className="space-y-1 text-center">
                  <CardTitle className="text-2xl font-semibold mb-2">
                    Reset password
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Enter your new password below to reset your account.
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                    <div className="space-y-1.5">
                      <Label htmlFor="password">New password</Label>
                      <div className="relative">
                        <Input
                          id="password"
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter new password"
                          {...register('password')}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOffIcon className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <EyeIcon className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                      </div>
                      <ErrorMessage error={errors.password} />
                    </div>

                    <div className="space-y-1.5">
                      <Label htmlFor="confirmPassword">
                        Confirm new password
                      </Label>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder="Confirm new password"
                          {...register('confirmPassword')}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                        >
                          {showConfirmPassword ? (
                            <EyeOffIcon className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <EyeIcon className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                      </div>
                      <ErrorMessage error={errors.confirmPassword} />
                    </div>

                    <Button
                      className="w-full text-base"
                      size="lg"
                      type="submit"
                      disabled={resetPasswordMutation.isPending}
                    >
                      {resetPasswordMutation.isPending ? (
                        <>
                          Resetting...
                          <Spinner className="ml-2" />
                        </>
                      ) : (
                        'Reset password'
                      )}
                    </Button>
                  </form>

                  <Link
                    href="/"
                    className="flex items-center justify-center gap-2 text-sm text-primary hover:underline"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Login
                  </Link>
                </CardContent>
              </>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}

These are example pages.