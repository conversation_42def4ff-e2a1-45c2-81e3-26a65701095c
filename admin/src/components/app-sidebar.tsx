'use client';
import * as React from 'react';
import { Car, LayoutDashboard, MapPin } from 'lucide-react';

import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import {
   Sidebar,
   SidebarContent,
   SidebarFooter,
   SidebarHeader,
   SidebarRail,
} from '@/components/ui/sidebar';

// Driver management data
const data = {
   user: {
      name: 'john doe',
      email: '<EMAIL>',
      avatar: '/avatars/shadcn.jpg',
   },
   navMain: [
      {
         title: 'Dashboard',
         url: '/dashboard',
         icon: LayoutDashboard,
         isActive: false,
      },
      {
         title: 'Drivers',
         url: '/drivers',
         icon: Car,
         isActive: true,
      },
      {
         title: 'Cities',
         url: '/cities',
         icon: MapPin,
         isActive: false,
      },
   ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   return (
      <Sidebar collapsible='icon' {...props}>
         <SidebarHeader>
            <div className='flex items-center px-4 py-2'>
               <h1 className='text-lg font-bold text-foreground'>Tukxi Admin</h1>
            </div>
         </SidebarHeader>
         <SidebarContent>
            <NavMain items={data.navMain} />
         </SidebarContent>
         <SidebarFooter>
            <NavUser user={data.user} />
         </SidebarFooter>
         <SidebarRail />
      </Sidebar>
   );
}
