export interface Driver {
  id: number;
  driverId: string;
  firstName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  phoneNumber: string;
  email: string;
  location: string;
  status: 'pending' | 'active' | 'inactive';
  progress: number;
  joinedDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface ListDriverResponse {
  data: Driver[];
  totalPages: number;
  currentPage: number;
  totalCount: number;
  perPage: number;
}

export interface CreateDriverRequest {
  firstName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  phoneNumber: string;
  email: string;
  location: string;
}

export interface UpdateDriverRequest extends Partial<CreateDriverRequest> {
  id: number;
  status?: 'pending' | 'active' | 'inactive';
}

export interface DriverFilters {
  page?: number;
  perPage?: number;
  search?: string;
  status?: 'pending' | 'active' | 'inactive';
  location?: string;
}
