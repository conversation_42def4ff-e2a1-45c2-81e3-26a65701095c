import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { CreateTakeoffResponse } from '../types/takeoff';
import { Driver, CreateDriverRequest, UpdateDriverRequest } from '../types/driver';
import { tryCatch } from '@/lib/try-catch';

/**
 * Hook for creating a new takeoff project
 * This mutation directly sends files to the API endpoint
 */
export const useCreateTakeoff = () => {
   return useMutation({
      mutationFn: async (data: {
         name: string;
         quotedPrice: number;
         dueDate: string;
         ai_system_prompt?: string;
         files: File[];
         onUploadProgress?: (progress: number) => void;
      }): Promise<CreateTakeoffResponse> => {
         // Create a FormData object to send both files and JSON data
         const formData = new FormData();

         // Add the JSON data
         formData.append('name', data.name);
         formData.append('submissionDate', new Date().toISOString());
         formData.append('quotedPrice', data.quotedPrice.toString());
         formData.append('dueDate', data.dueDate);
         formData.append('status', 'submitted');

         // Add ai_system_prompt if provided
         if (data.ai_system_prompt) {
            formData.append('ai_system_prompt', data.ai_system_prompt);
         }

         // Add all files to the FormData
         data.files.forEach(file => {
            formData.append('files', file);
         });

         // Set initial progress
         if (data.onUploadProgress) {
            data.onUploadProgress(0);
         }

         // Send the request with progress tracking
         const takeoffResult = await tryCatch(
            apiClient.post('/takeoff', formData, {
               onUploadProgress: progressEvent => {
                  if (data.onUploadProgress && progressEvent.total) {
                     // Calculate the percentage (cap at 95% until we get the response)
                     const percentCompleted = Math.min(
                        Math.round((progressEvent.loaded * 95) / progressEvent.total),
                        95
                     );
                     data.onUploadProgress(percentCompleted);
                  }
               },
               headers: {
                  'Content-Type': 'multipart/form-data',
               },
            })
         );

         if (takeoffResult.error) {
            console.error('Error creating takeoff:', takeoffResult.error);
            throw takeoffResult.error;
         }

         // Set progress to 100% when everything is complete
         if (data.onUploadProgress) {
            data.onUploadProgress(100);
         }

         // The apiClient.post returns the response data directly due to the response interceptor
         return takeoffResult.data as unknown as CreateTakeoffResponse;
      },
   });
};

/**
 * Hook for deleting a takeoff project
 */
export const useDeleteTakeoff = () => {
   return useMutation({
      mutationFn: async (id: number): Promise<void> => {
         return apiClient.delete(`/takeoff/${id}`);
      },
   });
};

/**
 * Hook for updating a takeoff project
 */
export const useUpdateTakeoff = () => {
   return useMutation({
      mutationFn: async (data: {
         id: number;
         name: string;
         quotedPrice: number;
         dueDate: string;
         status: string;
         ai_system_prompt?: string;
      }): Promise<CreateTakeoffResponse> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/takeoff/${id}`, payload);
      },
   });
};

/**
 * Hook for creating a new driver
 */
export const useCreateDriver = () => {
   return useMutation({
      mutationFn: async (data: CreateDriverRequest): Promise<Driver> => {
         return apiClient.post('/driver', data);
      },
   });
};

/**
 * Hook for updating a driver
 */
export const useUpdateDriver = () => {
   return useMutation({
      mutationFn: async (data: UpdateDriverRequest): Promise<Driver> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/driver/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a driver
 */
export const useDeleteDriver = () => {
   return useMutation({
      mutationFn: async (id: number): Promise<void> => {
         return apiClient.delete(`/driver/${id}`);
      },
   });
};
