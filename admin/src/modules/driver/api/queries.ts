import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { CreateTakeoffResponse, ListTakeoffResponse } from '../types/takeoff';
import { Driver, ListDriverResponse } from '../types/driver';

export interface ListTakeoffParams {
  page: number;
  perPage: number;
  search?: string;
  status?: 'submitted' | 'won' | 'lost';
  orderBySubmissionDate?: 'asc' | 'desc';
}

export const useListTakeoff = ({
  page,
  perPage,
  search,
  status,
  orderBySubmissionDate,
}: ListTakeoffParams) => {
  return useQuery({
    placeholderData: keepPreviousData,
    queryKey: ['takeoff', page, perPage, search, status, orderBySubmissionDate],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListTakeoffResponse> => {
      return apiClient.get('/takeoff', {
        params: {
          page,
          perPage,
          search,
          status,
          orderBySubmissionDate,
        },
      });
    },
  });
};

export const useGetTakeoff = (id: number | null) => {
  return useQuery({
    queryKey: ['takeoff', id],
    queryFn: (): Promise<CreateTakeoffResponse> => {
      if (!id) throw new Error('Takeoff ID is required');
      return apiClient.get(`/takeoff/${id}`);
    },
    enabled: !!id, // Only run the query if id is provided
  });
};

export interface ListDriverParams {
   page: number;
   perPage: number;
   search?: string;
   status?: 'pending' | 'active' | 'inactive';
   location?: string;
}

// Hardcoded driver data for testing
const mockDriverData: ListDriverResponse = {
   data: [
      {
         id: 1,
         driverId: 'DRV001',
         firstName: 'John',
         lastName: 'Doe',
         gender: 'male',
         dateOfBirth: '1990-05-15',
         phoneNumber: '+91 9876543210',
         email: '<EMAIL>',
         location: 'mumbai',
         status: 'pending',
         progress: 75,
         joinedDate: '2024-01-15',
         createdAt: '2024-01-15T10:00:00Z',
         updatedAt: '2024-01-15T10:00:00Z',
      },
      {
         id: 2,
         driverId: 'DRV002',
         firstName: 'Jane',
         lastName: 'Smith',
         gender: 'female',
         dateOfBirth: '1988-08-22',
         phoneNumber: '+91 9876543211',
         email: '<EMAIL>',
         location: 'delhi',
         status: 'active',
         progress: 100,
         joinedDate: '2024-01-10',
         createdAt: '2024-01-10T10:00:00Z',
         updatedAt: '2024-01-10T10:00:00Z',
      },
      {
         id: 3,
         driverId: 'DRV003',
         firstName: 'Raj',
         lastName: 'Patel',
         gender: 'male',
         dateOfBirth: '1992-03-10',
         phoneNumber: '+91 9876543212',
         email: '<EMAIL>',
         location: 'bangalore',
         status: 'inactive',
         progress: 45,
         joinedDate: '2024-01-20',
         createdAt: '2024-01-20T10:00:00Z',
         updatedAt: '2024-01-20T10:00:00Z',
      },
      {
         id: 4,
         driverId: 'DRV004',
         firstName: 'Priya',
         lastName: 'Sharma',
         gender: 'female',
         dateOfBirth: '1995-11-05',
         phoneNumber: '+91 9876543213',
         email: '<EMAIL>',
         location: 'chennai',
         status: 'pending',
         progress: 30,
         joinedDate: '2024-01-25',
         createdAt: '2024-01-25T10:00:00Z',
         updatedAt: '2024-01-25T10:00:00Z',
      },
      {
         id: 5,
         driverId: 'DRV005',
         firstName: 'Amit',
         lastName: 'Kumar',
         gender: 'male',
         dateOfBirth: '1987-07-18',
         phoneNumber: '+91 9876543214',
         email: '<EMAIL>',
         location: 'kolkata',
         status: 'active',
         progress: 90,
         joinedDate: '2024-01-05',
         createdAt: '2024-01-05T10:00:00Z',
         updatedAt: '2024-01-05T10:00:00Z',
      },
   ],
   totalPages: 1,
   currentPage: 1,
   totalCount: 5,
   perPage: 10,
};

export const useListDriver = ({ page, perPage, search, status, location }: ListDriverParams) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: ['driver', page, perPage, search, status, location],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListDriverResponse> => {
         // Return mock data for now - remove this when real API is integrated
         return Promise.resolve(mockDriverData);

         // Uncomment this when real API is ready:
         // return apiClient.get('/driver', {
         //    params: {
         //       page,
         //       perPage,
         //       search,
         //       status,
         //       location,
         //    },
         // });
      },
   });
};

export const useGetDriver = (id: number | null) => {
   return useQuery({
      queryKey: ['driver', id],
      queryFn: (): Promise<Driver> => {
         if (!id) throw new Error('Driver ID is required');
         return apiClient.get(`/driver/${id}`);
      },
      enabled: !!id, // Only run the query if id is provided
   });
};
