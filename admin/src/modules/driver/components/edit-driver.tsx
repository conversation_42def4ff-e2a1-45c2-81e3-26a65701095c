'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';

import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as z from 'zod';
import { useUpdateDriver } from '../api/mutations';
import { useGetDriver } from '../api/queries';
import { useQueryClient } from '@tanstack/react-query';

// Define the Zod schema for form validation
const editDriverFormSchema = z.object({
  firstName: z
    .string()
    .nonempty('First name is required')
    .min(2, 'First name must be at least 2 characters'),
  lastName: z
    .string()
    .nonempty('Last name is required')
    .min(2, 'Last name must be at least 2 characters'),
  gender: z.string().nonempty('Gender is required'),
  dateOfBirth: z.date({
    required_error: 'Date of birth is required',
  }),
  phoneNumber: z
    .string()
    .nonempty('Phone number is required')
    .min(10, 'Phone number must be at least 10 digits'),
  email: z
    .string()
    .nonempty('Email is required')
    .email('Please enter a valid email address'),
  location: z.string().nonempty('Location is required'),
  status: z.enum(['pending', 'active', 'inactive']),
});

// Infer the type from the schema
type EditDriverFormValues = z.infer<typeof editDriverFormSchema>;

interface EditDriverProps {
  driverId: number | null;
  isOpen: boolean;
  onClose: () => void;
}

export const EditDriver = ({ driverId, isOpen, onClose }: EditDriverProps) => {
  const updateDriverMutation = useUpdateDriver();
  const queryClient = useQueryClient();
  const { data: driver, isLoading } = useGetDriver(driverId);

  const form = useForm<EditDriverFormValues>({
    resolver: zodResolver(editDriverFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      gender: '',
      dateOfBirth: new Date(),
      phoneNumber: '',
      email: '',
      location: '',
      status: 'pending',
    },
  });

  const {
    formState: { errors },
    reset,
    control,
  } = form;

  // Reset form when driver data is loaded
  useEffect(() => {
    if (driver) {
      reset({
        firstName: driver.firstName,
        lastName: driver.lastName,
        gender: driver.gender,
        dateOfBirth: new Date(driver.dateOfBirth),
        phoneNumber: driver.phoneNumber,
        email: driver.email,
        location: driver.location,
        status: driver.status,
      });
    }
  }, [driver, reset]);

  const onSubmit = async (data: EditDriverFormValues) => {
    if (!driverId) return;

    updateDriverMutation.mutate(
      {
        id: driverId,
        firstName: data.firstName,
        lastName: data.lastName,
        gender: data.gender,
        dateOfBirth: data.dateOfBirth.toISOString(),
        phoneNumber: data.phoneNumber,
        email: data.email,
        location: data.location,
        status: data.status,
      },
      {
        onSuccess: () => {
          toast.success('Driver updated successfully');
          onClose();
          queryClient.invalidateQueries({ queryKey: ['driver'] });
        },
        onError: (error) => {
          toast.error(error);
        },
      },
    );
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent
        style={{
          maxWidth: '475px',
        }}
      >
        <form
          className="flex h-full w-full flex-col"
          onSubmit={form.handleSubmit(onSubmit)}
        >
          <SheetHeader className="flex-none">
            <SheetTitle>Edit Driver</SheetTitle>
            <SheetDescription>Update driver information</SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto" id="form-container">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-gray-500">Loading driver data...</div>
              </div>
            ) : (
              <div className="space-y-4 px-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex flex-col gap-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Controller
                      control={control}
                      name="firstName"
                      render={({ field }) => (
                        <Input
                          id="firstName"
                          placeholder="Enter first name"
                          {...field}
                          className="w-full"
                        />
                      )}
                    />
                    {errors.firstName && <ErrorMessage error={errors.firstName} />}
                  </div>

                  <div className="flex flex-col gap-2">
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Controller
                      control={control}
                      name="lastName"
                      render={({ field }) => (
                        <Input
                          id="lastName"
                          placeholder="Enter last name"
                          {...field}
                          className="w-full"
                        />
                      )}
                    />
                    {errors.lastName && <ErrorMessage error={errors.lastName} />}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex flex-col gap-2">
                    <Label htmlFor="gender">Gender *</Label>
                    <Controller
                      control={control}
                      name="gender"
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.gender && <ErrorMessage error={errors.gender} />}
                  </div>

                  <div className="flex flex-col gap-2">
                    <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                    <Controller
                      control={control}
                      name="dateOfBirth"
                      render={({ field }) => (
                        <Popover modal={true}>
                          <PopoverTrigger asChild>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground',
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'dd/MM/yyyy')
                              ) : (
                                <span>dd/mm/yyyy</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 z-[9999]" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                    {errors.dateOfBirth && <ErrorMessage error={errors.dateOfBirth} />}
                  </div>
                </div>

                <div className="flex flex-col gap-2">
                  <Label htmlFor="phoneNumber">Phone Number *</Label>
                  <Controller
                    control={control}
                    name="phoneNumber"
                    render={({ field }) => (
                      <Input
                        id="phoneNumber"
                        placeholder="+91 9876543210"
                        {...field}
                        className="w-full"
                      />
                    )}
                  />
                  {errors.phoneNumber && <ErrorMessage error={errors.phoneNumber} />}
                </div>

                <div className="flex flex-col gap-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Controller
                    control={control}
                    name="email"
                    render={({ field }) => (
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        className="w-full"
                      />
                    )}
                  />
                  {errors.email && <ErrorMessage error={errors.email} />}
                </div>

                <div className="flex flex-col gap-2">
                  <Label htmlFor="location">Location *</Label>
                  <Controller
                    control={control}
                    name="location"
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select location" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="mumbai">Mumbai, Maharashtra</SelectItem>
                          <SelectItem value="delhi">Delhi</SelectItem>
                          <SelectItem value="bangalore">Bangalore, Karnataka</SelectItem>
                          <SelectItem value="chennai">Chennai, Tamil Nadu</SelectItem>
                          <SelectItem value="kolkata">Kolkata, West Bengal</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.location && <ErrorMessage error={errors.location} />}
                </div>

                <div className="flex flex-col gap-2">
                  <Label htmlFor="status">Status *</Label>
                  <Controller
                    control={control}
                    name="status"
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">Pending Review</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.status && <ErrorMessage error={errors.status} />}
                </div>
              </div>
            )}
          </div>

          <SheetFooter className="flex-none p-4 grid grid-cols-2 gap-4 w-full border-t bg-white">
            <SheetClose asChild>
              <Button
                type="button"
                variant="outline"
                disabled={updateDriverMutation.isPending}
              >
                Cancel
              </Button>
            </SheetClose>
            <Button type="submit" disabled={updateDriverMutation.isPending || isLoading}>
              {updateDriverMutation.isPending ? 'Updating...' : 'Update Driver'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};
