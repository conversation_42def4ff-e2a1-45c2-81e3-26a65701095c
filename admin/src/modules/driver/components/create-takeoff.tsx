'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  FileUpload,
  FileUploadDropzone,
  FileUploadItem,
  FileUploadItemDelete,
  FileUploadItemMetadata,
  FileUploadItemPreview,
  FileUploadList,
  FileUploadTrigger,
} from '@/components/ui/file-upload';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon, Plus, Upload, X } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as z from 'zod';
import { useCreateTakeoff } from '../api/mutations';
import { ProgressLoader } from './file-upload-loader';
import { useQueryClient } from '@tanstack/react-query';

// Define the Zod schema for form validation
const takeoffFormSchema = z.object({
  name: z
    .string()
    .nonempty('Takeoff name is required')
    .min(2, 'Takeoff name must be at least 2 characters'),
  quotedPrice: z.coerce.number().min(0, 'Price must be a positive number'),
  dueDate: z.date({
    required_error: 'Due date is required',
  }),
  ai_system_prompt: z.string().optional(),
  files: z
    .array(z.instanceof(File))
    .min(1, 'At least one blueprint file is required'),
});

// Infer the type from the schema
type TakeoffFormValues = z.infer<typeof takeoffFormSchema>;

export const CreateTakeoff = () => {
  const [open, setOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const createTakeoffMutation = useCreateTakeoff();
  const loaderRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  const form = useForm<TakeoffFormValues>({
    resolver: zodResolver(takeoffFormSchema),
    defaultValues: {
      name: '',
      quotedPrice: 0,
      dueDate: new Date(), // Default to today's date
      ai_system_prompt: '',
      files: [],
    },
  });

  const {
    formState: { errors },
    watch,
    setValue,
    reset,
    control,
  } = form;

  // Watch files from the form
  const files = watch('files');

  const onFileValidate = React.useCallback(
    (file: File): string | null => {
      // Validate max files
      if (files.length >= 2) {
        return 'You can only upload up to 2 files';
      }

      if (!file.type.endsWith('pdf')) {
        // Validate file type (only pdf)
        return 'Only pdf files are allowed';
      }

      // Validate file size (max 50MB)
      const oneMb = 1024 * 1024;
      const MAX_SIZE = 50 * oneMb;
      if (file.size > MAX_SIZE) {
        return `File size must be less than ${MAX_SIZE / oneMb}MB`;
      }

      return null;
    },
    [files],
  );

  const onFileReject = React.useCallback((_file: File, message: string) => {
    toast.error(message);
  }, []);

  // Handle file changes with React Hook Form
  const handleFileChange = (newFiles: File[]) => {
    setValue('files', newFiles, { shouldValidate: true });
  };

  const onSubmit = async (data: TakeoffFormValues) => {
    // Reset progress when starting a new upload
    setUploadProgress(0);

    // Call the mutation to upload files and create takeoff using tryCatch
    createTakeoffMutation.mutate(
      {
        name: data.name,
        quotedPrice: data.quotedPrice,
        dueDate: data.dueDate.toISOString(), // Convert Date to ISO string
        ai_system_prompt: data.ai_system_prompt,
        files: data.files,
        onUploadProgress: (progress) => {
          setUploadProgress(progress);
          // Scroll to the loader element when upload starts
          if (progress > 0 && loaderRef.current) {
            loaderRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            });
          }
        },
      },
      {
        onSuccess: () => {
          toast.success('Takeoff created successfully');
          reset({
            name: '',
            quotedPrice: 0,
            dueDate: new Date(),
            ai_system_prompt: '',
            files: [],
          });
          setUploadProgress(0);
          setOpen(false);
          queryClient.invalidateQueries({ queryKey: ['takeoff'] });
        },
        onError: (error) => {
          toast.error(error);
          setUploadProgress(0);
        },
      },
    );
  };

  return (
    <Sheet
      open={open}
      onOpenChange={(open) => {
        setOpen(open);

        if (!open) {
          return false;
        }
      }}
    >
      <SheetTrigger asChild>
        <Button
          className="cursor-pointer"
          id="create-takeoff-trigger"
          variant="outline"
        >
          <Plus />
          New Takeoff
        </Button>
      </SheetTrigger>
      <SheetContent
        style={{
          maxWidth: '475px',
        }}
      >
        <form
          className="flex h-full w-full flex-col"
          onSubmit={form.handleSubmit(onSubmit)}
        >
          <SheetHeader className="flex-none">
            <SheetTitle>New Takeoff</SheetTitle>
            <SheetDescription>Create a new takeoff</SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto" id="form-container">
            <div className="space-y-4 px-4 py-4">
              <div className="flex flex-col gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <Input
                        id="name"
                        placeholder="Enter takeoff name"
                        {...field}
                        className="w-full"
                      />
                    )}
                  />
                  {errors.name && <ErrorMessage error={errors.name} />}
                </div>
              </div>

              <div className="flex flex-col gap-4">
                <Label htmlFor="quotedPrice" className="text-right">
                  Quoted Price ($)
                </Label>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="quotedPrice"
                    render={({ field }) => (
                      <Input
                        id="quotedPrice"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="Enter quoted price"
                        {...field}
                        className="w-full"
                      />
                    )}
                  />
                  {errors.quotedPrice && (
                    <ErrorMessage error={errors.quotedPrice} />
                  )}
                </div>
              </div>

              <div className="flex flex-col gap-4 mb-8">
                <Label htmlFor="dueDate" className="text-right">
                  Due Date
                </Label>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="dueDate"
                    render={({ field }) => (
                      <div className="w-full">
                        <Popover modal={true}>
                          <PopoverTrigger asChild>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground',
                              )}
                              onClick={(e) => {
                                // Prevent event from propagating to parent elements
                                e.stopPropagation();
                              }}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-auto p-0 z-[9999]"
                            align="start"
                            sideOffset={5}
                            onInteractOutside={(e) => {
                              // Prevent closing when clicking inside the calendar
                              if (
                                e.target &&
                                (e.target as HTMLElement).closest('.rdp')
                              ) {
                                e.preventDefault();
                              }
                            }}
                          >
                            <div onClick={(e) => e.stopPropagation()}>
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={(date) => {
                                  field.onChange(date);
                                  // Just update the value without additional event handling
                                }}
                                initialFocus
                              />
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    )}
                  />
                  {errors.dueDate && <ErrorMessage error={errors.dueDate} />}
                </div>
              </div>

              <div className="flex flex-col gap-4 mb-8">
                <div>
                  <Label htmlFor="ai_system_prompt" className="text-right mb-1">
                    AI System Prompt (Optional)
                  </Label>
                  <span className="text-sm text-muted-foreground">
                    Provide specific instructions for the AI to follow
                  </span>
                </div>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="ai_system_prompt"
                    render={({ field }) => (
                      <Textarea
                        id="ai_system_prompt"
                        placeholder="Enter AI system prompt (optional)"
                        {...field}
                        className="w-full min-h-[100px]"
                        rows={4}
                      />
                    )}
                  />
                  {errors.ai_system_prompt && (
                    <ErrorMessage error={errors.ai_system_prompt} />
                  )}
                </div>
              </div>

              <div className="flex flex-col gap-4">
                <div>
                  <Label htmlFor="blueprints" className="text-right mb-1">
                    Upload Blueprints
                  </Label>

                  <span className="text-sm text-muted-foreground">
                    Only PDF files are allowed. You may upload a maximum of 2
                    files, with each file not exceeding 50MB.
                  </span>
                </div>

                <FileUpload
                  id="blueprints"
                  value={files}
                  onValueChange={handleFileChange}
                  onFileValidate={onFileValidate}
                  onFileReject={onFileReject}
                  accept=".pdf"
                  maxFiles={2}
                  className="w-full max-w-md"
                  multiple
                  style={{
                    maxWidth: '440px',
                  }}
                >
                  <FileUploadDropzone>
                    <div className="flex flex-col items-center gap-1">
                      <div className="flex items-center justify-center rounded-full border p-2.5">
                        <Upload className="size-6 text-muted-foreground" />
                      </div>
                      <p className="font-medium text-sm">
                        Drag & drop files here
                      </p>
                      <p className="text-muted-foreground text-xs">
                        Or click to browse (max 2 files)
                      </p>
                    </div>
                    <FileUploadTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 w-fit"
                      >
                        Browse files
                      </Button>
                    </FileUploadTrigger>
                  </FileUploadDropzone>
                  <FileUploadList>
                    {files.map((file, idx) => (
                      <FileUploadItem key={`${file.name}_${idx}`} value={file}>
                        <FileUploadItemPreview />
                        <FileUploadItemMetadata />
                        <FileUploadItemDelete asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-7"
                          >
                            <X />
                          </Button>
                        </FileUploadItemDelete>
                      </FileUploadItem>
                    ))}
                  </FileUploadList>

                  {errors.files && <ErrorMessage error={errors.files} />}
                </FileUpload>
              </div>

              {createTakeoffMutation.isPending && (
                <div ref={loaderRef} id="loader-section">
                  <ProgressLoader
                    value={uploadProgress}
                    status={'loading'}
                    className="max-w-[440px] mx-auto"
                  />
                </div>
              )}
            </div>
          </div>

          <SheetFooter className="flex-none p-4 grid grid-cols-2 gap-4 w-full border-t bg-white">
            <SheetClose asChild>
              <Button
                type="button"
                variant="outline"
                disabled={createTakeoffMutation.isPending}
              >
                Close
              </Button>
            </SheetClose>
            <Button type="submit" disabled={createTakeoffMutation.isPending}>
              {createTakeoffMutation.isPending ? 'Submitting...' : 'Submit'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};
