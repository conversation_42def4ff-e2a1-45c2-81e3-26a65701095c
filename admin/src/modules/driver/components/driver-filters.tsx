'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Search, X, Filter, MapPin } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';

export interface DriverFiltersProps {
  onSearchChange: (search: string) => void;
  onStatusChange: (status: string | undefined) => void;
  onLocationChange: (location: string | undefined) => void;
  search: string;
  status: string | undefined;
  location: string | undefined;
}

export function DriverFilters({
  onSearchChange,
  onStatusChange,
  onLocationChange,
  search,
  status,
  location,
  isLoading,
}: DriverFiltersProps & { isLoading?: boolean }) {
  const [searchValue, setSearchValue] = useState(search || '');
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local search state when prop changes
  useEffect(() => {
    setSearchValue(search || '');
  }, [search]);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle search input with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // Show searching indicator
    setIsSearching(true);

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set a new timeout
    searchTimeoutRef.current = setTimeout(() => {
      onSearchChange(value);
      searchTimeoutRef.current = null;
      setIsSearching(false);
    }, 500); // 500ms debounce time
  };

  // Clear all filters
  const handleClearFilters = () => {
    // Clear any pending search timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    setIsSearching(false);
    setSearchValue('');
    onSearchChange('');
    onStatusChange(undefined);
    onLocationChange(undefined);
  };

  // Check if any filters are active
  const hasActiveFilters = !!search || !!status || !!location;

  return (
    <div className="flex flex-col space-y-4 mb-4">
      <div className="flex flex-wrap gap-2 items-center">
        {/* Search Input */}
        <div className="relative flex-1 min-w-[200px]">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search by name, phone, email, or ID..."
            value={searchValue}
            onChange={handleSearchChange}
            className="pl-8"
          />
          {(isSearching || (isLoading && searchValue)) && (
            <div className="absolute right-2.5 top-2.5 text-gray-500">
              <Spinner className="h-4 w-4 text-primary" />
            </div>
          )}
          {searchValue && !isSearching && !isLoading && (
            <button
              onClick={() => {
                // Clear any pending search timeout
                if (searchTimeoutRef.current) {
                  clearTimeout(searchTimeoutRef.current);
                  searchTimeoutRef.current = null;
                }
                setIsSearching(false);
                setSearchValue('');
                onSearchChange('');
              }}
              className="absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Status Filter */}
        <Select
          value={status || 'all'}
          onValueChange={(value) =>
            onStatusChange(value === 'all' ? undefined : value)
          }
        >
          <SelectTrigger className="w-[150px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending Review</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>

        {/* Location Filter */}
        <Select
          value={location || 'all'}
          onValueChange={(value) =>
            onLocationChange(value === 'all' ? undefined : value)
          }
        >
          <SelectTrigger className="w-[180px]">
            <MapPin className="h-4 w-4 mr-2" />
            <SelectValue placeholder="All Locations" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Locations</SelectItem>
            <SelectItem value="mumbai">Mumbai, Maharashtra</SelectItem>
            <SelectItem value="delhi">Delhi</SelectItem>
            <SelectItem value="bangalore">Bangalore, Karnataka</SelectItem>
            <SelectItem value="chennai">Chennai, Tamil Nadu</SelectItem>
            <SelectItem value="kolkata">Kolkata, West Bengal</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearFilters}
            className="text-gray-700 border-gray-300"
          >
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
}
