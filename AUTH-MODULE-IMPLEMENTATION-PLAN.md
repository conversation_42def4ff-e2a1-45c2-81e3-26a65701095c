# Authentication Module Implementation Plan

## Overview
This document outlines the detailed implementation plan for creating the complete authentication module with forgot password, OTP verification, and password reset functionality for the Tukxi Admin portal.

## Current State Analysis
- ✅ Login page exists with proper styling and structure
- ✅ Auth module structure is established
- ✅ React Hook Form + Zod validation pattern is in place
- ✅ TanStack Query for API calls is configured
- ✅ Basic routing structure exists
- ⚠️ Forgot password page exists but is incomplete
- ❌ OTP verification page missing
- ❌ Reset password page missing
- ❌ API mutations for new endpoints missing
- ❌ Type definitions for new flows missing

## Design System & Styling
Based on the existing login page, we'll maintain consistency with:
- **Layout**: `bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10`
- **Container**: `flex w-full max-w-sm flex-col gap-6`
- **Card Structure**: Using `Card`, `CardContent`, `CardHeader`, `CardTitle` components
- **Form Styling**: Single input per row at 100% width
- **Color Scheme**: Base color #4361EE, clean/minimalistic aesthetic
- **Branding**: "Tukxi Admin" instead of team switcher

## API Endpoints
1. **Login**: `POST /api/v1/auth/password/login`
2. **Forgot Password**: `POST /api/v1/auth/forgot-password`
3. **Verify OTP**: `POST /api/v1/auth/verify-forgot-password-otp`
4. **Reset Password**: `POST /api/v1/auth/reset-password`

## Implementation Tasks

### 1. Type Definitions
**File**: `admin/src/modules/auth/types/auth.ts`
- Create comprehensive type definitions for all auth flows
- Include payload and response types for each endpoint
- Extend existing login types

### 2. API Mutations
**File**: `admin/src/modules/auth/api/mutations.ts`
- Add `useForgotPassword` mutation
- Add `useVerifyForgotPasswordOtp` mutation  
- Add `useResetPassword` mutation
- Update existing mutations file

### 3. Validation Schemas
**File**: `admin/src/modules/auth/schema/auth-schemas.ts`
- Create Zod schemas for forgot password form
- Create Zod schema for OTP verification (6-digit)
- Create Zod schema for reset password form with confirmation
- Reuse existing password schema

### 4. Shared Components
**File**: `admin/src/modules/auth/components/auth-header.tsx`
- Create reusable header component with Tukxi Admin branding
- Include LogoName component for consistency

### 5. Page Components

#### 5.1 Forgot Password Page
**File**: `admin/src/modules/auth/pages/forgot-password-page.tsx`
- Email input form with validation
- Submit button with loading state
- Navigation back to login
- Success handling redirects to OTP verification

#### 5.2 OTP Verification Page  
**File**: `admin/src/modules/auth/pages/verify-otp-page.tsx`
- 6-digit OTP input component
- Resend OTP functionality with countdown timer
- Email display (masked for privacy)
- Success handling redirects to reset password

#### 5.3 Reset Password Page
**File**: `admin/src/modules/auth/pages/reset-password-page.tsx`
- New password input with show/hide toggle
- Confirm password input with validation
- Success state with completion message
- Navigation back to login after success

### 6. App Router Pages

#### 6.1 Update Forgot Password Route
**File**: `admin/src/app/auth/forgot-password/page.tsx`
- Replace placeholder with actual ForgotPasswordPage component

#### 6.2 Create OTP Verification Route
**File**: `admin/src/app/auth/verify-otp/page.tsx`
- Create new route for OTP verification
- Handle email and OTP as URL search parameters

#### 6.3 Create Reset Password Route
**File**: `admin/src/app/auth/reset-password/page.tsx`
- Create new route for password reset
- Handle email and OTP as URL search parameters

### 7. Navigation Flow
1. **Login → Forgot Password**: Add "Forgot Password?" link to login form
2. **Forgot Password → OTP Verification**: Redirect with email in URL params
3. **OTP Verification → Reset Password**: Redirect with email + OTP in URL params
4. **Reset Password → Login**: Redirect after successful reset

### 8. URL Parameter Strategy
To prevent refresh errors and maintain state:
- **OTP Verification**: `/auth/verify-otp?email=<EMAIL>`
- **Reset Password**: `/auth/reset-password?email=<EMAIL>&otp=1234`

### 9. Error Handling
- Implement consistent error messaging using existing toast system
- Handle API errors gracefully with user-friendly messages
- Validate URL parameters and redirect to appropriate pages if missing

### 10. Loading States
- Implement loading spinners for all async operations
- Disable form inputs during API calls
- Show appropriate loading text for each action

## File Structure After Implementation
```
admin/src/modules/auth/
├── api/
│   └── mutations.ts (updated)
├── components/
│   ├── auth-header.tsx (new)
│   └── login-form.tsx (updated with forgot password link)
├── pages/
│   ├── forgot-password-page.tsx (new)
│   ├── login-page.tsx (existing)
│   ├── reset-password-page.tsx (new)
│   └── verify-otp-page.tsx (new)
├── schema/
│   ├── auth-schemas.ts (new)
│   └── password-schema.ts (existing)
└── types/
    ├── auth.ts (new)
    └── login.ts (existing)

admin/src/app/auth/
├── forgot-password/
│   └── page.tsx (updated)
├── reset-password/
│   └── page.tsx (new)
└── verify-otp/
    └── page.tsx (new)
```

## Testing Strategy
After implementation:
1. Test complete forgot password flow end-to-end
2. Test OTP resend functionality
3. Test password validation requirements
4. Test URL parameter handling and refresh scenarios
5. Test error states and edge cases
6. Verify responsive design on different screen sizes

## Dependencies
All required dependencies are already available:
- `react-hook-form` ✅
- `@hookform/resolvers/zod` ✅
- `zod` ✅
- `@tanstack/react-query` ✅
- `lucide-react` (for icons) ✅
- Existing UI components ✅

## Next Steps
1. Start with type definitions and API mutations
2. Create shared components and schemas
3. Implement page components following the existing design patterns
4. Set up routing and navigation
5. Test the complete flow
6. Add forgot password link to login form
